# METRo README file

METRo stands for "Model of the Environment and Temperature of Roads".
It is a road weather forecast software. With the help of observations 
provided by roads weather stations (road weather information system, RWIS) 
and the atmospheric forecast, METRo can, amongst other things, predict 
the roads conditions with particular interest such as: freezing rain, 
accumulation of snow, frost and thaw. 

METRo is Free under the terms of the GPL license and is proudly provided by Environment Canada, Government of Canada.
Copyright (C) 2006 Environment Canada

Packages included in the directory metro/src/frontend/external_lib are NOT part 
of METRo but are distributed under the condition of their corresponding license (Python License, Apache License)

It is developped and maintained by the staff of the Centre météorologique 
canadien (CMC), Montréal, Québec.

METRo is mainly written in python but also use fortran and C (to link the python 
and the fortran). 


## Documentation 

Documentation can be found online at:
https://framagit.org/metroprojects/metro/wikis/home

## Contact us 

You can contact the METRo developer team [through the METRo users mailing list](https://framalistes.org/sympa/info/metro-users) at the address: 
[<EMAIL>](mailto:<EMAIL>)
