# translation of metro_validate.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_validate\n"
"POT-Creation-Date: Mon Nov  8 15:55:03 2004\n"
"PO-Revision-Date: 2004-11-09 09:26-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_validate.py:15
msgid "class %s is a virtual class"
msgstr "La classe %s est une classe virtuelle"

#: metro_validate.py:41
msgid "Fatal Error when validating %s "
msgstr "Erreur fatale pendant la validation de %s "

#: metro_validate.py:42
msgid ""
"XML string.\n"
"The error is:\n"
"%s"
msgstr ""
"chaîne de caractères XML.\n"
"L'erreur est:\n"
"%s"

#: metro_validate.py:47
msgid "%s XML string has been validated"
msgstr "La chaîne de caractères XML %s a été validée"

#: metro_validate.py:52
msgid "Fatal Error, %s XML string is empty"
msgstr "Erreur fatale, la chaîne de caractères %s est vide"

#: metro_validate.py:57
msgid "Fatal Error, no %s XML string to validate."
msgstr "Erreur fatale, aucune chaîne de caractères XML %s à valider."

#: metro_validate.py:72
msgid "Error when validating "
msgstr "Erreur pendant la validation "

#: metro_validate.py:73
msgid "XML string."
msgstr "de la chaîne de caractères XML."

#: metro_validate.py:76
msgid "No XML string to validate"
msgstr "Aucune chaîne de caractères XML à valider"

