# translation of metro_xml.po to 
# translation of metro_xml.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004, 2006, 2007.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml\n"
"POT-Creation-Date: 2007-03-05 18:17+UTC\n"
"PO-Revision-Date: 2007-03-05 18:47+0000\n"
"Last-Translator: \n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.9.1\n"

#: ../src/frontend/toolbox/metro_xml.py:60
msgid "Fatal error! Can't import '%s' xml library"
msgstr "Erreur fatale! Incapable d'importer la librairie XML '%s'"

#: ../src/frontend/toolbox/metro_xml.py:67
msgid "XML library '%s' will be use."
msgstr "La librairie XML: '%s' sera utilisée."

#: ../src/frontend/toolbox/metro_xml.py:79
msgid "Fatal error! No METRo XML library can be use. "
msgstr "Erreur fatale! Aucune librairie XML de METRo ne peut être utilisée. "

#: ../src/frontend/toolbox/metro_xml.py:80
msgid ""
"\n"
"METRo need one of the following XML library "
msgstr ""
"\n"
"METRo nécessite une des librairies XML suivante "

#: ../src/frontend/toolbox/metro_xml.py:81
msgid ""
"installed on the system.\n"
"Supported library:"
msgstr ""
"installée sur le système.\n"
"Librairies supportées:"

#: ../src/frontend/toolbox/metro_xml.py:87
msgid ""
"metro_xml_pyxml will be used.\n"
"WE STRONGLY "
msgstr ""
"metro_xml_pyxml sera utilisée.\n"
"NOUS RECOMMANDONS"

#: ../src/frontend/toolbox/metro_xml.py:88
msgid "RECOMMAND THAT YOU USED libxml2, METRo"
msgstr "FORTEMENT D'UTILISER libxml2, METRo"

#: ../src/frontend/toolbox/metro_xml.py:89
msgid "WOULD BE 10 TIMES FASTER."
msgstr "SERA 10 FOIS PLUS RAPIDE."

#: ../src/frontend/toolbox/metro_xml.py:99

msgid "metro_xml_libxml2 will be used."
msgstr "metro_xml_libxml2 sera utilisée."

#: ../src/frontend/toolbox/metro_xml.py:115
msgid "Validation is only supported with metro_xml_libxml2"
msgstr "La validation n'est seulement supportée qu'avec metro_xml_libxml2"

#: ../src/frontend/toolbox/metro_xml.py:178
msgid "Invalid data_type: (%s) for the "
msgstr "Invalide data_type: (%s) à la place "

#: ../src/frontend/toolbox/metro_xml.py:180
msgid "following tag:(%s). Default data "
msgstr "de l'étiquette suivante:(%s). Le type de données"

#: ../src/frontend/toolbox/metro_xml.py:182
msgid "type will be used."
msgstr "par défaut sera utilisé."

#: ../src/frontend/toolbox/metro_xml.py:300
msgid "Invalid data_type: (%s) for the following tag:(%s)."
msgstr "Invalide data_type: (%s) à la place de l'étiquette suivante:(%s)."

#: ../src/frontend/toolbox/metro_xml.py:302
msgid " Default data type will be used."
msgstr "Le type de données par défaut sera utilisé."

#: ../src/frontend/toolbox/metro_xml.py:365
msgid "Invalid data_type: (%s) for the following "
msgstr "Invalide data_type: (%s) à la place de l'étiquette "

#: ../src/frontend/toolbox/metro_xml.py:367
msgid "tag:(%s). Default data type will be used."
msgstr " suivante:(%s).Le type de données par défaut sera utilisé."

