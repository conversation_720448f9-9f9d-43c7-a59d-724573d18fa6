# translation of metro_config_validation.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_config_validation\n"
"POT-Creation-Date: 2009-02-11 19:49+UTC\n"
"PO-Revision-Date: 2004-11-09 10:35-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: toolbox/metro_config_validation.py:50
msgid "Internal METRo configuration"
msgstr "Configuration interne de METRo"

#: toolbox/metro_config_validation.py:52
msgid "Hardcoded value"
msgstr "Valeur écrite en dur"

#: toolbox/metro_config_validation.py:54
msgid "Configuration file"
msgstr "Fichier de configuration"

#: toolbox/metro_config_validation.py:56
msgid "Command line parameter"
msgstr "Paramètre de ligne de commande"

#: toolbox/metro_config_validation.py:60
msgid "Configuration error:\n"
msgstr "Erreur de configuration:\n"

#: toolbox/metro_config_validation.py:62
msgid "Config item: %s\n"
msgstr "Élément de configuration: %s\n"

#: toolbox/metro_config_validation.py:63
msgid "Set from:    %s.\n"
msgstr "Assigné depuis:   %s.\n"

#: toolbox/metro_config_validation.py:64
msgid ""
"The following error occured:\n"
"%s"
msgstr ""
"L'erreur suivante s'est produite:\n"
"%s"

#: toolbox/metro_config_validation.py:89
msgid "Some XML definition are not complete.\n"
msgstr "Certaines défnition XML ne sont pas complète.\n"

#: toolbox/metro_config_validation.py:90
msgid "Each definition must have the following property "
msgstr "Chaque définition doivent avoir assigné la propriété suivante "

#: toolbox/metro_config_validation.py:91
msgid ""
"set:\n"
"NAME, XML_TAG, DATA_TYPE"
msgstr ""
":\n"
"NAME, XML_TAG, DATA_TYPE"

#: toolbox/metro_config_validation.py:110
msgid "In item %s, '%s' is not a valid METRo data type."
msgstr "Dans l'élément %s, '%s' n'est pas un type de donnée valide pour METRo."

#: toolbox/metro_config_validation.py:112
msgid ""
"\n"
"Valid METRo data type are:\n"
"[%s]"
msgstr ""
"\n"
"Les données valides pour METRo sont:\n"
"[%s]"

#: toolbox/metro_config_validation.py:218
msgid ""
"Fatal error, the date string '%s' passed to the\n"
" "
msgstr ""
"Erreur fatale, la chaîne de caractère pour la date '%s' de \n"
" "

#: toolbox/metro_config_validation.py:220
msgid "option '--roadcast-start-date' doesn't conform to "
msgstr "l'option --roadcast-start-date' n'est pas conforme à "

#: toolbox/metro_config_validation.py:221
msgid "ISO 8601"
msgstr "ISO 8601"

#: toolbox/metro_config_validation.py:228
msgid "No roadcast start date provided. The date of the\n"
msgstr ""
"Aucune date de départ pour la prévision routière n'est fournie. La date \n"

#: toolbox/metro_config_validation.py:229
msgid "last observation will be used as the roadcast "
msgstr "de la dernière observation sera utilisée comme "

#: toolbox/metro_config_validation.py:230
msgid "start date\n"
msgstr "date de départ \n"

msgid "'%s' is not a valid road layer type.\n"
msgstr "'%s' n'est pas un type de couche valide.\n"

msgid ""
"Valid road layer type are:\n"
"[%s]"
msgstr ""
"Les types de couche valides sont:\n"
"[%s]"
