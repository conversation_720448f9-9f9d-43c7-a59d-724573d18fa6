# translation of metro_xml_libxml2.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004, 2006.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml_libxml2\n"
"POT-Creation-Date: 2006-11-16 21:16+UTC\n"
"PO-Revision-Date: 2006-11-16 21:37+0000\n"
"Last-Translator: \n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.9.1\n"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:60
msgid "XML Error!: %s"
msgstr "Erreur XML!: %s"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:87
msgid "LIBXML2 Memory leak %d bytes"
msgstr "Fuite de mémoire dans LIBXML2, %d octets"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:149
msgid "At least one error occured when validating XML file."
msgstr "Au moins une erreur est survenue lors de la validation du fichier XML."

