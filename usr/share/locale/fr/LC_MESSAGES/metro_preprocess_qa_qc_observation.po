# translation of metro_preprocess_qa_qc_observation.po to
# translation of metro_preprocess_qa_qc_observation.po to
# translation of metro_preprocess_qa_qc_observation.po to
# translation of metro_preprocess_qa_qc_observation.po to
# Copyright (C) 2007 Environnement Canada.
#
# <PERSON> <<EMAIL>>, 2004, 2005, 2006, 2014.
msgid ""
msgstr ""
"Project-Id-Version: metro_preprocess_qa_qc_observation\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-10-06 14:34+0000\n"
"PO-Revision-Date: 2014-10-06 14:42+0000\n"
"Last-Translator: tremblay\n"
"Language-Team: American English <<EMAIL>>\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Lokalize 1.4\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:97
msgid "No valid observations. Halting METRo"
msgstr "Aucune observation valide. Arrêt de METRo"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:131
msgid "No valid observation"
msgstr "Aucune observation valide"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:175
msgid "Invalid road temperature"
msgstr "Température de la route invalide"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:180
#, python-format
msgid "%d th  temperature is %f"
msgstr "La %d ième  temperature est %f"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:226
#, python-format
msgid "More than %d minutes between 2 measures"
msgstr "Plus de %d minutes entre 2 mesures"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:233
#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:282
#, python-format
msgid "Indice: %d"
msgstr "Indice: %d"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:236
#, python-format
msgid "Cutoff time: day:%d hour:%d minute:%d"
msgstr "Heure de coupure: jour:%d heure:%d minute:%d"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:252
msgid "Time of observation are not in order. "
msgstr "Les temps d'observation ne sont pas en ordre. "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:253
#, python-format
msgid "Check the %d th value"
msgstr "Vérifiez la %d ième valeur"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:275
#, python-format
msgid "Observation is more than %d hours"
msgstr "Les observations datent de plus de %d heures"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:277
msgid "before the first roadcast"
msgstr "avant la première heure de prévision routière"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:304
msgid "Observation after the first roadcast time of METRo"
msgstr "Les observations sont après la première heure de prévision routière"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:308
#, python-format
msgid "Threshold: %d"
msgstr "Seuil: %d"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:315
#, python-format
msgid "Time difference: %f"
msgstr "Différence de temps: %f"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:356
#, python-format
msgid "No valid sub-surface temperature (element <sst>) in observation file %s"
msgstr ""
"Aucune température sous la route n'est valide (élément  <sst>) dans le "
"fichier d'observation %s"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:465
#, python-format
msgid "First atmospheric forecast: %s"
msgstr "Date de la première prévision atmosphérique: %s"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:470
#, python-format
msgid "First valid observation   : %s"
msgstr "Première observation valide:                 %s"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:505
msgid "Option '--fix-deep-soil-temperature' is used while "
msgstr "Option '--fix-deep-soil-temperature' est utilisée alors "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:506
msgid "the station is of type BRIDGE. Deep soil "
msgstr "que la station est de type PONT. La température "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:507
msgid "Temperature  will not be used."
msgstr "profonde du sol ne sera pas utilisée."

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:516
msgid "Deep soil temperature following the option "
msgstr "La température du sol profond suivant l'option "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:517
msgid "'-fix-deep-soil-temperature' must be between "
msgstr "'-fix-deep-soil-temperature' doit être entre "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:518
msgid "boundaries ["
msgstr "les bornes ["

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:527
msgid "Using deep soil temperature: "
msgstr "Température de la route invalide: "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:528
msgid " degree Celsius"
msgstr "degrés Celsius"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:532
msgid "Option '-fix-deep-soil-temperature' must be "
msgstr "Option '-fix-deep-soil-temperature' doit être "

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:533
msgid "followed by a numeric value. The given value '"
msgstr "suivie par une valeur numérique. La valeur donnée '"

#: src/frontend/executable_module/metro_preprocess_qa_qc_observation.py:534
msgid "' is not a number."
msgstr "' n'est pas un nombre."

