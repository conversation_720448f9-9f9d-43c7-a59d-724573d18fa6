# translation of metro_write.po to 
# translation of metro_write.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_write\n"
"POT-Creation-Date: Tue Nov  9 09:46:06 2004\n"
"PO-Revision-Date: 2004-11-09 09:50-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_write.py:34
msgid "start writing file:'%s'"
msgstr "Début de l'écriture du fichier: '%s'"

#: metro_write.py:39
msgid "An error occured when writing XML file: '%s' "
msgstr "Une erreur est survenue pendant l'écriture du fichier XML:'%s' "

#: metro_write.py:44
msgid "XML file: '%s' written with success"
msgstr "Fichier XML: '%s' écrit avec succès"

