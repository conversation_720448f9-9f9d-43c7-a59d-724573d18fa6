# translation of metro_dom2metro.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_dom2metro\n"
"POT-Creation-Date: Thu Dec  2 11:52:38 2004\n"
"PO-Revision-Date: 2004-12-02 11:58-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: src/frontend/executable_module/metro_dom2metro.py:93
#: src/frontend/executable_module/metro_dom2metro.py:148
#: src/frontend/executable_module/metro_dom2metro.py:208
#: src/frontend/executable_module/metro_dom2metro.py:266
msgid "XML error in file '%s'."
msgstr "Erreur XML dans le fichier '%s'"

#: src/frontend/executable_module/metro_dom2metro.py:328
#: src/frontend/executable_module/metro_dom2metro.py:334
#: src/frontend/executable_module/metro_dom2metro.py:342
msgid "An error occured when reading "
msgstr "Une erreur est survenu lors de la lecture du "

#: src/frontend/executable_module/metro_dom2metro.py:329
msgid ""
"file:\n"
"'%s'.\n"
"The error is:\n"
"'%s'."
msgstr ""
"fichier:\n"
"'%s'.\n"
"L'erreur est:\n"
"'%s'."

#: src/frontend/executable_module/metro_dom2metro.py:335
#: src/frontend/executable_module/metro_dom2metro.py:343
msgid ""
"file:\n"
"'%s'.\n"
"The error is:\n"
"'%s'.\n"
msgstr ""
"fichier:\n"
"'%s'.\n"
"L'erreur est:\n"
"'%s'.\n"

#: src/frontend/executable_module/metro_dom2metro.py:337
#: src/frontend/executable_module/metro_dom2metro.py:345
msgid "METRo will try to read the file."
msgstr "METRo va essayer de lire le fichier."

