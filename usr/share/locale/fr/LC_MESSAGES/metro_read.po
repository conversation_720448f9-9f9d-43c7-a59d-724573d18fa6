# translation of metro_read.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read\n"
"POT-Creation-Date: Mon Nov  8 13:08:34 2004\n"
"PO-Revision-Date: 2004-11-08 13:09-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read.py:14
msgid "class %s is a virtual class"
msgstr "La classe %s est une classe virtuelle"

#: metro_read.py:38
msgid ""
"Error when opening file:\n"
"%s"
msgstr ""
"Erreur à l'ouverture du fichier:\n"
"%s"

#: metro_read.py:43
msgid "File '%s' read with success"
msgstr "Fichier '%s' lu avec succès"

