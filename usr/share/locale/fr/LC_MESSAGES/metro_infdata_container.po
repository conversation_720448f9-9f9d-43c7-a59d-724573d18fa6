# translation of metro_infdata_container.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_infdata_container\n"
"POT-Creation-Date: Fri Nov  5 16:04:11 2004\n"
"PO-Revision-Date: 2004-11-05 16:09-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_infdata_container.py:29
msgid "'%s' is not a valid infdata name\n"
msgstr "'%s' n'est pas un nom d'infdata valide\n"

#: metro_infdata_container.py:30
msgid ""
"Try one of the following infdata name:\n"
"%s"
msgstr ""
"Essayez un des nom d'infdata suivant:\n"
"%s"

