# translation of metro_string2dom.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_string2dom\n"
"POT-Creation-Date: Mon Nov  8 13:53:09 2004\n"
"PO-Revision-Date: 2004-11-08 13:56-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_string2dom.py:17
msgid "class %s is a virtual class"
msgstr "La classe %s est une classe virtuelle"

#: metro_string2dom.py:39
msgid "Error when converting string "
msgstr "Erreur lors de la conversion de la chaîne de caractères "

#: metro_string2dom.py:40
msgid "to DOM."
msgstr "à un DOM."

#: metro_string2dom.py:41
msgid "Error when converting string to DOM. "
msgstr "Erreur pendant la conversion de la chaîne de caractères à un DOM. "

#: metro_string2dom.py:42
msgid ""
"The string is:\n"
"%s"
msgstr ""
"La chaîne de caractères est:\n"
"%s"

#: metro_string2dom.py:45
msgid "No string to convert"
msgstr "Aucune chaîne de caractères à convertir"

