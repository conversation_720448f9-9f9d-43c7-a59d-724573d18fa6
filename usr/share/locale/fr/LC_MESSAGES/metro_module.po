# translation of metro_module.po to 
# translation of metro_module.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_module\n"
"POT-Creation-Date: Mon Nov  8 10:35:40 2004\n"
"PO-Revision-Date: 2004-11-08 11:43-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_module.py:32
msgid "class %s is a virtual class"
msgstr "La classe %s est une classe virtuelle"

#: metro_module.py:39
msgid "%s module: start execution"
msgstr "module %s: début de l'exécution"

#: metro_module.py:44
msgid "%s module: end of execution"
msgstr "module %s: fin de l'exécution"

#: metro_module.py:49
msgid "%s module: receiving data"
msgstr "module %s: réception des données"

#: metro_module.py:56
msgid ""
"%s module:\n"
"sending data to module %s"
msgstr ""
"module %s:\n"
"envoie des données au module %s"

#: metro_module.py:65
msgid "class %s misses method 'get_receive_type'"
msgstr "Il manque la méthode 'get_receive_type' dans la classe %s"

#: metro_module.py:71
msgid "class %s misses method 'get_send_type'"
msgstr "Il manque la méthode 'set_receive_type' dans la classe %s"

