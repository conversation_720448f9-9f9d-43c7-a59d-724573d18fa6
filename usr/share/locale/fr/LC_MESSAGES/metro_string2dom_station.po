# translation of metro_string2dom_station.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_string2dom_station\n"
"POT-Creation-Date: Mon Nov  8 15:49:09 2004\n"
"PO-Revision-Date: 2004-11-08 15:50-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_string2dom_station.py:32
msgid "Fatal Error when converting station "
msgstr "Erreur fatale pendant la conversion de la chaîne \nde caractères "

#: metro_string2dom_station.py:33
msgid ""
"string to DOM. The error is:\n"
"%s"
msgstr ""
"au DOM. L'erreur est:\n"
"%s"

#: metro_string2dom_station.py:39
msgid "Fatal Error, no station string to convert."
msgstr "Erreur fatale, aucune chaîne de caractères pour la station à convertir."

