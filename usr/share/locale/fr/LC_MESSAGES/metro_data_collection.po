# translation of metro_data_collection.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_data_collection\n"
"POT-Creation-Date: Fri Nov  5 15:32:07 2004\n"
"PO-Revision-Date: 2004-11-05 15:32-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_data_collection.py:44 metro_data_collection.py:53
msgid ""
"Invalid attribute name. Valid attribute name are:\n"
"%s"
msgstr ""
"Invalid attribute name. Valid attribute name are:\n"
"%s"

#: metro_data_collection.py:67
msgid "Attribute name '%s' already used"
msgstr "Attribute name '%s' already used"

