# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004, 2006.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_model\n"
"POT-Creation-Date: 2006-11-16 21:16+UTC\n"
"PO-Revision-Date: 2006-11-16 21:24+0000\n"
"Last-Translator: \n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.9.1\n"

#: ../src/frontend/executable_module/metro_model.py:87
msgid "Bypassing METRo core, roadcast not created."
msgstr "Contournement du noyau de METRo, aucune prévision routière n'est créée"

#: ../src/frontend/executable_module/metro_model.py:130
msgid "year: [%s]"
msgstr "année: [%s]"

#: ../src/frontend/executable_module/metro_model.py:135
msgid "month: [%s]"
msgstr "mois: [%s]"

#: ../src/frontend/executable_module/metro_model.py:140
msgid "day: [%s]"
msgstr "jour: [%s]"

#: ../src/frontend/executable_module/metro_model.py:145
msgid "hour: [%s]"
msgstr "heure: [%s]"

#: ../src/frontend/executable_module/metro_model.py:179
msgid "Number of layer="
msgstr "Nombre de couche="

#: ../src/frontend/executable_module/metro_model.py:188
msgid "roadlayer type="
msgstr "Type de couche de la route="

#: ../src/frontend/executable_module/metro_model.py:191
msgid "roadlayer thick="
msgstr "Épaisseur de la couche="

#: ../src/frontend/executable_module/metro_model.py:199
msgid "timezone="
msgstr "fuseau horaire="

#: ../src/frontend/executable_module/metro_model.py:214
msgid "issue time="
msgstr "Heure de la première prévision:"

#: ../src/frontend/executable_module/metro_model.py:276
msgid "------------station config START---------------------"
msgstr "------DÉMARRAGE de la configuration de la station----"

#: ../src/frontend/executable_module/metro_model.py:283
msgid "Short time zone = "
msgstr "Code de 3 lettres du fuseau horaire = "

#: ../src/frontend/executable_module/metro_model.py:290
msgid "lat,lon: "
msgstr "lat,lon: "

#: ../src/frontend/executable_module/metro_model.py:298
msgid "fLCorr is: "
msgstr "fLCorr est: "

#: ../src/frontend/executable_module/metro_model.py:302
msgid "------------station config END---------------------"
msgstr "------------FIN de la configuration de la station---------------------"

#: ../src/frontend/executable_module/metro_model.py:309
msgid "Start sending data to METRo core"
msgstr "Début de l'envoie des données au noyau de METRo"

#: ../src/frontend/executable_module/metro_model.py:324
msgid "Fatal error in METRo physical model."
msgstr "Erreur fatale dans le modèle physique de METRo."

#: ../src/frontend/executable_module/metro_model.py:329
msgid "End of METRo core"
msgstr "Fin de l'execution du noyau de METRo"

