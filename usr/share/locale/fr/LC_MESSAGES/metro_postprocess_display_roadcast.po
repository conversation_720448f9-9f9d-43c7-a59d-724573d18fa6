# translation of metro_postprocess_display_roadcast.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_postprocess_display_roadcast\n"
"POT-Creation-Date: Mon Nov  8 11:05:51 2004\n"
"PO-Revision-Date: 2004-11-08 11:09-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_postprocess_display_roadcast.py:48
msgid "Error when importing the package "
msgstr "Erreur dans l'importation du paquetage "

#: metro_postprocess_display_roadcast.py:49
msgid ""
"metro_graphics.\n"
"Check if the directory %s "
msgstr ""
"metro_graphics.\n"
"Vérifiez si le répertoire '%s' "

#: metro_postprocess_display_roadcast.py:50
msgid "exists and if the file "
msgstr "existe et si le fichier "

#: metro_postprocess_display_roadcast.py:51
msgid "metro_graphics_roadcast.py is in this "
msgstr "metro_graphics_roadcast.py est dans "

#: metro_postprocess_display_roadcast.py:52
msgid ""
"this directory.\n"
"Python return the following"
msgstr ""
"ce répertoire.\n"
"Python retourne l'erreur"

#: metro_postprocess_display_roadcast.py:53
msgid "error:'%s'"
msgstr "suivante: '%s'"

