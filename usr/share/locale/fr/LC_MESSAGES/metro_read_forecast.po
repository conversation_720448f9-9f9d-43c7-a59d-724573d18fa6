# translation of metro_read_forecast.po to 
# translation of metro_read_forecast.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read_forecast\n"
"POT-Creation-Date: Mon Nov  8 13:14:00 2004\n"
"PO-Revision-Date: 2004-11-08 15:21-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read_forecast.py:33
msgid "METRo need a valid forecast file."
msgstr "METRo nécessite un fichier de prévision atmosphérique valide."

#: metro_read_forecast.py:43
msgid "METRo need a forecast file, please use the "
msgstr "METRo nécessite un fichier de prévision atmosphérique valide,\n svp utilisez "

#: metro_read_forecast.py:44
msgid "option: '--input-forecast'"
msgstr "l'option: '--input-forecast'"

