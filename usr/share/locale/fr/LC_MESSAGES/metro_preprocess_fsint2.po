# translation of metro_preprocess_fsint2.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_preprocess_fsint2\n"
"POT-Creation-Date: Tue Nov 16 16:21:17 2004\n"
"PO-Revision-Date: 2004-11-16 16:26-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: ../src/frontend/executable_module/metro_preprocess_fsint2.py:147
msgid "For the date %d-%d-%d,\n"
msgstr "Pour la date %d-%d-%d,\n"

#: ../src/frontend/executable_module/metro_preprocess_fsint2.py:149
msgid "at the latitude %0.2f "
msgstr "à la latitude %0.2f "

#: ../src/frontend/executable_module/metro_preprocess_fsint2.py:150
msgid " and longitude %0.2f "
msgstr "et à la longitude %0.2f "

#: ../src/frontend/executable_module/metro_preprocess_fsint2.py:151
msgid ""
"\n"
"sunrise is at %d:%d:%d UTC\n"
msgstr ""
"\n"
"l'aurore est à %d:%d:%d UTC\n"

#: ../src/frontend/executable_module/metro_preprocess_fsint2.py:153
msgid "sunset  is at %d:%d:%d UTC"
msgstr "L'aube est à   %d:%d:%d UTC"

