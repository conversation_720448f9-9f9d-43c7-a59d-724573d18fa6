# translation of metro_xml_pyxml.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml_pyxml\n"
"POT-Creation-Date: Tue Nov  9 13:32:15 2004\n"
"PO-Revision-Date: 2004-11-09 13:32-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_xml_pyxml.py:18
msgid "XML Warning!: %s"
msgstr "Avertissement XML! %s"

#: metro_xml_pyxml.py:27
msgid "XML Error!: %s"
msgstr "Erreur XML!: %s"

#: metro_xml_pyxml.py:36
msgid "XML Fatal Error!: %s"
msgstr "Erreur fatale XML!: %s"

