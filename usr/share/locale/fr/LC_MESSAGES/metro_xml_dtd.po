# translation of metro_xml_dtd.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml_dtd\n"
"POT-Creation-Date: Tue Nov  9 13:23:29 2004\n"
"PO-Revision-Date: 2004-11-09 13:24-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_xml_dtd.py:40
msgid "Generating METRo DTD catalog..."
msgstr "Création du catalogue DTD de METRo..."

#: metro_xml_dtd.py:84
msgid "can't create METRo DTD catalog file:'%s'"
msgstr "Impossible de créer le fichier du catalogue DTD de METRo:'%s'"

#: metro_xml_dtd.py:93
msgid "METRo DTD catalog file created with success.\n"
msgstr "Catalogue DTD de METRo créé avec succès.\n"

#: metro_xml_dtd.py:94
msgid "DTD catalog file:'%s'"
msgstr "Fichier du catalogue DTD:'%s'"

