# translation of metro_postprocess_subsample_roadcast.po to 
# translation of metro_postprocess_subsample_roadcast.po to
# translation of metro_postprocess_subsample_roadcast.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004, 2007.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_postprocess_subsample_roadcast\n"
"POT-Creation-Date: 2007-03-05 18:17+UTC\n"
"PO-Revision-Date: 2007-03-05 18:40+0000\n"
"Last-Translator: \n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.9.1\n"

#: ../src/frontend/executable_module/metro_postprocess_subsample_roadcast.py:114

msgid "Roadcast start date set to: ''%s'"
msgstr "Date de départ de la prévision routière utilisé: '%s'"

#: ../src/frontend/executable_module/metro_postprocess_subsample_roadcast.py:124
msgid "Unable to determine the first time of roadcast!"
msgstr "Impossible de déterminer la première date pour la prévision routière!"

#: ../src/frontend/executable_module/metro_postprocess_subsample_roadcast.py:157

msgid "Output file roadcast start date: '%s'"
msgstr "Date de départ de la prévision routière dans le fichier de sortie: '%s'"

#: ../src/frontend/executable_module/metro_postprocess_subsample_roadcast.py:202

msgid "Roadcast start date of the model is: '%s'"
msgstr "Date de départ de la prévision routière dans le modèle: '%s'"

