# translation of metro_read_observation_ref.po to 
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read_observation_ref\n"
"POT-Creation-Date: Mon Nov  8 13:24:45 2004\n"
"PO-Revision-Date: 2004-11-08 13:27-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read_observation_ref.py:30
msgid "No file was read. METRo may need this "
msgstr "Aucun fichier n'a été lu. METRo pourrait avoir besoin "

#: metro_read_observation_ref.py:31
msgid "file content later."
msgstr "du contenu de ce fichier plus tard."

#: metro_read_observation_ref.py:42
msgid "If you want to read a second observation file,"
msgstr "Si vous dérirez lire un second fichier d'observation,"

#: metro_read_observation_ref.py:43
msgid ""
"\n"
"please use the option: "
msgstr ""
"\n"
"utilisez l'option: "

#: metro_read_observation_ref.py:44
msgid "'--input-observation-ref'."
msgstr "'--input-observation-ref'."

#: metro_read_observation_ref.py:45
msgid ""
"\n"
"You can safely remove this module from the "
msgstr ""
"\n"
"Vous pouvez sans crainte enlevez ce module "

#: metro_read_observation_ref.py:46
msgid ""
"EXECUTION SEQUENCE\n"
"if you don't need it."
msgstr ""
"de la sequence d'exécution\n"
"si vous n'en avez pas besoin."

