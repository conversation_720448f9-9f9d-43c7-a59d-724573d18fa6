# translation of metro_date.po to
# Copyright (C) 2007 Environnement Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_date\n"
"POT-Creation-Date: Thu Nov 18 11:58:42 2004\n"
"PO-Revision-Date: 2004-11-18 12:34-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: src/frontend/toolbox/metro_date.py:30
#, fuzzy
msgid "The following error occured when parsing the "
msgstr "L'erreur suivante est survenue lors de l'analyse du format ISO8601 "

#: src/frontend/toolbox/metro_date.py:31
#, fuzzy
msgid "ISO 8601 date: %s"
msgstr "de la date:%s"

#: src/frontend/toolbox/metro_date.py:38
#, fuzzy
msgid "The following error occured when parsing a "
msgstr "L'erreur suivante est survenue lors de l'analyse du format ISO8601 "

#: src/frontend/toolbox/metro_date.py:39
msgid ""
"date:\n"
"No date string to convert"
msgstr ""
"date:\n"
"No date string to convert"

#: src/frontend/toolbox/metro_date.py:53
msgid "Init time zone"
msgstr "Fuseau horaire inconnue: "

#: src/frontend/toolbox/metro_date.py:55
msgid "Time zone used: '%s'"
msgstr "Time zone used: '%s'"

#: src/frontend/toolbox/metro_date.py:187
msgid "Unknown time zone: %s"
msgstr "Fuseau horaire inconnue: %s"

#: src/frontend/toolbox/metro_date.py:237
msgid "Invalid criteria in get_elapsed_time: sUnit = %s"
msgstr "Critère invalide dans get_elapsed_time: sUnit = %s"

#: src/frontend/toolbox/metro_date.py:238
msgid "Error: invalid parameter"
msgstr "Erreur: paramètre invalide"

