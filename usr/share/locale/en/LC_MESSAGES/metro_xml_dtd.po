# translation of metro_xml_dtd.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml_dtd\n"
"POT-Creation-Date: Tue Nov  9 13:23:01 2004\n"
"PO-Revision-Date: 2004-11-09 13:23-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_xml_dtd.py:40
msgid "Generating METRo DTD catalog..."
msgstr "Generating METRo DTD catalog..."

#: metro_xml_dtd.py:84
msgid "can't create METRo DTD catalog file:'%s'"
msgstr "can't create METRo DTD catalog file:'%s'"

#: metro_xml_dtd.py:93
msgid "METRo DTD catalog file created with success.\n"
msgstr "METRo DTD catalog file created with success.\n"

#: metro_xml_dtd.py:94
msgid "DTD catalog file:'%s'"
msgstr "DTD catalog file:'%s'"

