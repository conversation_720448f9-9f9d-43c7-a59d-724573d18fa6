# translation of etro_preprocess_interpol_observation.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: etro_preprocess_interpol_observation\n"
"POT-Creation-Date: Mon Nov  8 11:36:51 2004\n"
"PO-Revision-Date: 2004-11-08 11:37-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_preprocess_interpol_observation.py:33
msgid "Not enough observation to do the interpolation"
msgstr "Not enough observation to do the interpolation"

#: metro_preprocess_interpol_observation.py:34
msgid "No valid observation.  Aborting"
msgstr "No valid observation.  Aborting"

#: metro_preprocess_interpol_observation.py:120
msgid "No valid observation in: "
msgstr "No valid observation in: "

