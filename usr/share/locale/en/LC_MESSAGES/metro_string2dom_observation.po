# translation of metro_string2dom_observation.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_string2dom_observation\n"
"POT-Creation-Date: Mon Nov  8 15:30:22 2004\n"
"PO-Revision-Date: 2004-11-08 15:42-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_string2dom_observation.py:31
msgid "Fatal Error when converting observation "
msgstr "Fatal Error when converting observation "

#: metro_string2dom_observation.py:32
msgid ""
"string to DOM. The error is:\n"
"%s"
msgstr ""
"string to DOM. The error is:\n"
"%s"

#: metro_string2dom_observation.py:38
msgid "Fatal Error, no observation string to convert."
msgstr "Fatal Error, no observation string to convert."

