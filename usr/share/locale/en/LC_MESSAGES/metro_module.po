# translation of metro_module.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_module\n"
"POT-Creation-Date: Mon Nov  8 10:34:57 2004\n"
"PO-Revision-Date: 2004-11-08 10:35-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_module.py:32
msgid "class %s is a virtual class"
msgstr "class %s is a virtual class"

#: metro_module.py:39
msgid "%s module: start execution"
msgstr "%s module: start execution"

#: metro_module.py:44
msgid "%s module: end of execution"
msgstr "%s module: end of execution"

#: metro_module.py:49
msgid "%s module: receiving data"
msgstr "%s module: receiving data"

#: metro_module.py:56
msgid ""
"%s module:\n"
"sending data to module %s"
msgstr ""
"%s module:\n"
"sending data to module %s"

#: metro_module.py:65
msgid "class %s misses method 'get_receive_type'"
msgstr "class %s misses method 'get_receive_type'"

#: metro_module.py:71
msgid "class %s misses method 'get_send_type'"
msgstr "class %s misses method 'get_send_type'"

