# translation of metro_validate_observation_ref.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_validate_observation_ref\n"
"POT-Creation-Date: Tue Nov  9 09:28:54 2004\n"
"PO-Revision-Date: 2004-11-09 09:35-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_validate_observation_ref.py:18
msgid "Error, no observation_ref string to validate.\n"
msgstr "Erreur, aucune chaÃ®ne de caractÃ¨res Ã  valider pour observation_ref.\n"

#: metro_validate_observation_ref.py:19
msgid "You can safely remove this module from the "
msgstr "Vous pouvez sans crainte retirer ce module "

#: metro_validate_observation_ref.py:20
msgid ""
"EXECUTION SEQUENCE\n"
"if you don't need it"
msgstr ""
"de la sÃ©quence d'exÃ©cution\n"
"si vous n'en avez pas besoin."

