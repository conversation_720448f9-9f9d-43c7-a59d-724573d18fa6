# translation of metro_read_station.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read_station\n"
"POT-Creation-Date: Mon Nov  8 13:39:20 2004\n"
"PO-Revision-Date: 2004-11-08 13:39-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read_station.py:31
msgid "METRo need a valid station file."
msgstr "METRo need a valid station file."

#: metro_read_station.py:41
msgid "METRo need a station file, please use the "
msgstr "METRo need a station file, please use the "

#: metro_read_station.py:42
msgid "option: '--input-station'"
msgstr "option: '--input-station'"

