# translation of metro_read_forecast.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read_forecast\n"
"POT-Creation-Date: Mon Nov  8 13:13:28 2004\n"
"PO-Revision-Date: 2004-11-08 13:13-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read_forecast.py:33
msgid "METRo need a valid forecast file."
msgstr "METRo need a valid forecast file."

#: metro_read_forecast.py:43
msgid "METRo need a forecast file, please use the "
msgstr "METRo need a forecast file, please use the "

#: metro_read_forecast.py:44
msgid "option: '--input-forecast'"
msgstr "option: '--input-forecast'"

