# translation of metro_read_observation.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_read_observation\n"
"POT-Creation-Date: Mon Nov  8 13:17:47 2004\n"
"PO-Revision-Date: 2004-11-08 13:18-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_read_observation.py:33
msgid "METRo need a valid observation file."
msgstr "METRo need a valid observation file."

#: metro_read_observation.py:43
msgid "METRo need an observation file, please use "
msgstr "METRo need an observation file, please use "

#: metro_read_observation.py:44
msgid "the option: '--input-observation'"
msgstr "the option: '--input-observation'"

