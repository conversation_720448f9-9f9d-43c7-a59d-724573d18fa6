# translation of metro_xml.po to 
# translation of metro_xml.po to
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004, 2006, 2007.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml\n"
"POT-Creation-Date: 2007-03-05 18:17+UTC\n"
"PO-Revision-Date: 2007-03-05 18:48+0000\n"
"Last-Translator: \n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.9.1\n"

#: ../src/frontend/toolbox/metro_xml.py:60
msgid "Fatal error! Can't import '%s' xml library"
msgstr "Fatal error! Can't import '%s' xml library"

#: ../src/frontend/toolbox/metro_xml.py:67
msgid "XML library '%s' will be use."
msgstr "XML library '%s' will be use."

#: ../src/frontend/toolbox/metro_xml.py:79
msgid "Fatal error! No METRo XML library can be use. "
msgstr "Fatal error! No METRo XML library can be use. "

#: ../src/frontend/toolbox/metro_xml.py:80
msgid ""
"\n"
"METRo need one of the following XML library "
msgstr ""
"\n"
"METRo need one of the following XML library "

#: ../src/frontend/toolbox/metro_xml.py:81
msgid ""
"installed on the system.\n"
"Supported library:"
msgstr ""
"installed on the system.\n"
"Supported library:"

#: ../src/frontend/toolbox/metro_xml.py:87
msgid ""
"metro_xml_pyxml will be used.\n"
"WE STRONGLY "
msgstr ""
"metro_xml_pyxml will be used.\n"
"WE STRONGLY "

#: ../src/frontend/toolbox/metro_xml.py:88
msgid "RECOMMAND THAT YOU USED libxml2, METRo"
msgstr "RECOMMAND THAT YOU USED libxml2, METRo"

#: ../src/frontend/toolbox/metro_xml.py:89
msgid "WOULD BE 10 TIMES FASTER."
msgstr "WOULD BE 10 TIMES FASTER."

#: ../src/frontend/toolbox/metro_xml.py:99

msgid "metro_xml_libxml2 will be used."
msgstr "metro_xml_libxml2 will be used."

#: ../src/frontend/toolbox/metro_xml.py:115
msgid "Validation is only supported with metro_xml_libxml2"
msgstr "Validation is only supported with metro_xml_libxml2"

#: ../src/frontend/toolbox/metro_xml.py:178
msgid "Invalid data_type: (%s) for the "
msgstr "Invalid data_type: (%s) for the "

#: ../src/frontend/toolbox/metro_xml.py:180
msgid "following tag:(%s). Default data "
msgstr "following tag:(%s). Default data "

#: ../src/frontend/toolbox/metro_xml.py:182
msgid "type will be used."
msgstr "type will be used."

#: ../src/frontend/toolbox/metro_xml.py:300
msgid "Invalid data_type: (%s) for the following tag:(%s)."
msgstr "Invalid data_type: (%s) for the following tag:(%s)."

#: ../src/frontend/toolbox/metro_xml.py:302
msgid " Default data type will be used."
msgstr " Default data type will be used."

#: ../src/frontend/toolbox/metro_xml.py:365
msgid "Invalid data_type: (%s) for the following "
msgstr "Invalid data_type: (%s) for the following "

#: ../src/frontend/toolbox/metro_xml.py:367
msgid "tag:(%s). Default data type will be used."
msgstr "tag:(%s). Default data type will be used."

