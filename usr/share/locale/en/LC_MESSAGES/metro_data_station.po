# English translations for METRo package.
# Copyright (C) 2007 Environment Canada.
# This file is distributed under the same license as the METRo package.
# <PERSON> <<EMAIL>>, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_data_station\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-04-10 19:52+0000\n"
"PO-Revision-Date: 2004-11-05 15:50-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=ASCII\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: metro_data_station.py:114
#, python-format
msgid "Wrong argument in the station config file %s."
msgstr "Wrong argument in the station config file %s."

#: metro_data_station.py:116
msgid "Station type must be \"road\" or \"bridge\". "
msgstr "Station type must be \"road\" or \"bridge\". "

#: metro_data_station.py:117
msgid "Using \"road\" as default."
msgstr "Using \"road\" as default."

#: metro_data_station.py:142
#, fuzzy, python-format
msgid ""
"Sensor SST depth value in station config file is not between 0.01 m and 1.4 "
"m. Given value: '%s' m."
msgstr ""
"Sensor SST depth value in station config file is not between 0.01 m and 1.4. "
"m. Given value: '%s' m."

