# translation of metro_util.po to 
# translation of metro_util.po to
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_util\n"
"POT-Creation-Date: Thu Dec  2 11:52:40 2004\n"
"PO-Revision-Date: 2004-12-02 11:55-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: src/frontend/toolbox/metro_util.py:28
msgid "The executable 'metro.py' must be in one of the following directory:\n"
msgstr "The executable 'metro.py' must be in one of the following directory:\n"

#: src/frontend/toolbox/metro_util.py:29
msgid "metro_directory/src/frontend' or 'metro_directory/bin'.\n"
msgstr "metro_directory/src/frontend' or 'metro_directory/bin'.\n"

#: src/frontend/toolbox/metro_util.py:30
msgid ""
"The following path is not valid: '%s'.\n"
"\n"
msgstr ""
"The following path is not valid: '%s'.\n"
"\n"

#: src/frontend/toolbox/metro_util.py:31
msgid "Aborting execution of METRo.\n"
msgstr "Aborting execution of METRo.\n"

#: src/frontend/toolbox/metro_util.py:142
msgid "In interpolate, the arrays does not"
msgstr "In interpolate, the arrays does not"

#: src/frontend/toolbox/metro_util.py:143
msgid "have the same length. %d != %d\n"
msgstr "have the same length. %d != %d\n"

#: src/frontend/toolbox/metro_util.py:148
msgid "Padding Y array with mean at the end."
msgstr "Padding Y array with mean at the end."

#: src/frontend/toolbox/metro_util.py:157
msgid "In interpolate, the arrays have only one value(1)"
msgstr "In interpolate, the arrays have only one value(1)"

#: src/frontend/toolbox/metro_util.py:162
msgid "In interpolate, iIncrement is too big. \n"
msgstr "In interpolate, iIncrement is too big. \n"

#: src/frontend/toolbox/metro_util.py:163
msgid "Space between two values in xArray:"
msgstr "Space between two values in xArray:"

#: src/frontend/toolbox/metro_util.py:201
msgid "In shift_left, npInput is not of size (1,).\n"
msgstr "In shift_left, npInput is not of size (1,).\n"

#: src/frontend/toolbox/metro_util.py:243
msgid "In shift_right, npInput is not of size (1,).\n"
msgstr "In shift_right, npInput is not of size (1,).\n"

#: src/frontend/toolbox/metro_util.py:285
msgid "No indice with this value: %d"
msgstr "No indice with this value: %d"

#: src/frontend/toolbox/metro_util.py:346
msgid "Cannot determine the sign of zero"
msgstr "Cannot determine the sign of zero"

#: src/frontend/toolbox/metro_util.py:397
msgid "In metro_util.subsample, subsampling rate"
msgstr "In metro_util.subsample, subsampling rate"

#: src/frontend/toolbox/metro_util.py:398
msgid "is higher than array size: %d > %d"
msgstr "is higher than array size: %d > %d"

#: src/frontend/toolbox/metro_util.py:441
msgid "Array are not of the same size"
msgstr "Array are not of the same size"

#: src/frontend/toolbox/metro_util.py:442
msgid "cutting the firts one"
msgstr "cutting the firts one"

#: src/frontend/toolbox/metro_util.py:449
msgid "In metro_util.concat_array, array must be"
msgstr "In metro_util.concat_array, array must be"

#: src/frontend/toolbox/metro_util.py:450
msgid " of the same dimension: %d != %d"
msgstr " of the same dimension: %d != %d"

#: src/frontend/toolbox/metro_util.py:511
msgid "Version number:'%s' is too old. Version from '%s' "
msgstr "Version number:'%s' is too old. Version from '%s' "

#: src/frontend/toolbox/metro_util.py:513
#: src/frontend/toolbox/metro_util.py:526
msgid "to '%s' inclusively are supported"
msgstr "to '%s' inclusively are supported"

#: src/frontend/toolbox/metro_util.py:518
msgid "Version number:'%s' is not yet supported. Version "
msgstr "Version number:'%s' is not yet supported. Version "

#: src/frontend/toolbox/metro_util.py:520
msgid "from '%s' to '%s' inclusively are supported"
msgstr "from '%s' to '%s' inclusively are supported"

#: src/frontend/toolbox/metro_util.py:524
msgid "Can't find version number. Version from '%s' "
msgstr "Can't find version number. Version from '%s' "

