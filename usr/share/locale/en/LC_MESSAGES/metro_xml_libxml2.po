# translation of metro_xml_libxml2.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_xml_libxml2\n"
"POT-Creation-Date: 2006-12-13 21:07+UTC\n"
"PO-Revision-Date: 2004-11-09 13:30-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:60
msgid "XML Error!: %s"
msgstr "XML Error!: %s"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:87
msgid "LIBXML2 Memory leak %d bytes"
msgstr "LIBXML2 Memory leak %d bytes"

#: ../src/frontend/toolbox/metro_xml_libxml2.py:149
msgid "At least one error occured when validating XML file."
msgstr "At least one error occured when validating XML file."
