# translation of metro_metro2dom.po to 
# Copyright (C) 2007 Environment Canada.
# <PERSON> <<EMAIL>>, 2004.
#
msgid ""
msgstr ""
"Project-Id-Version: metro_metro2dom\n"
"POT-Creation-Date: Mon Nov  8 09:04:44 2004\n"
"PO-Revision-Date: 2004-11-08 09:05-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team:  <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: KBabel 1.0.2\n"

#: metro_metro2dom.py:44
msgid "No forecast, can't create DOM forecast"
msgstr "No forecast, can't create DOM forecast"

#: metro_metro2dom.py:53
msgid "No roadcast, can't create DOM roadcast"
msgstr "No roadcast, can't create DOM roadcast"

