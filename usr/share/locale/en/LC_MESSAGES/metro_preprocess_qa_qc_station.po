# English translations for PACKAGE package.
# Copyright (C) 2014 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2014.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-10-16 19:45+0000\n"
"PO-Revision-Date: 2014-10-16 19:57+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=ASCII\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: executable_module/metro_preprocess_qa_qc_station.py:87
msgid "Option --enable-sunshadow is given but there is no "
msgstr "Option --enable-sunshadow is given but there is no "

#: executable_module/metro_preprocess_qa_qc_station.py:88
msgid ""
"azimuth data in station configuration file.\n"
" "
msgstr ""
"azimuth data in station configuration file.\n"
" "

#: executable_module/metro_preprocess_qa_qc_station.py:89
#: executable_module/metro_preprocess_qa_qc_station.py:101
msgid "Please correct this or remove the option --enable-sunshadow"
msgstr "Please correct this or remove the option --enable-sunshadow"

#: executable_module/metro_preprocess_qa_qc_station.py:99
msgid "Azimuth data in station configuration file "
msgstr "Azimuth data in station configuration file "

#: executable_module/metro_preprocess_qa_qc_station.py:100
msgid ""
"is not ordered by equal growing azimuths.\n"
" "
msgstr ""
"is not ordered by equal growing azimuths.\n"
" "

#: executable_module/metro_preprocess_qa_qc_station.py:108
msgid "Azimuth data does not have a value at 0 and/or 360 degrees. "
msgstr "Azimuth data does not have a value at 0 and/or 360 degrees. "

#: executable_module/metro_preprocess_qa_qc_station.py:109
msgid ""
"Please add one of this value to have a complete horizon.\n"
" "
msgstr ""
"Please add one of this value to have a complete horizon.\n"
" "
