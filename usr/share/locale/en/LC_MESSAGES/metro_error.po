# translation of metro_error.py to
# Copyright (C) 2010 Environnement Canada.
# <PERSON> <<EMAIL>>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: metro 3.2.5\n"
"POT-Creation-Date: 2010-07-25 10:53+EDT\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: pygettext.py 1.5\n"


#: metro_error.py:62
msgid "Error in import: "
msgstr "Error in import: "

#: metro_error.py:72
msgid "Error in metro_util.py: "
msgstr "Error in metro_util.py: "

#: metro_error.py:81
msgid "METR<PERSON>'s version error: "
msgstr "METRo's version error: "

#: metro_error.py:90
msgid "Date error: "
msgstr "Date error: "

#: metro_error.py:108
msgid "Input error: "
msgstr "Input error: "

#: metro_error.py:118
msgid "Data error: "
msgstr "Data error: "

