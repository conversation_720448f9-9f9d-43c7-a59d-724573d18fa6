test:
  image: ubuntu:20.04
  script:
  - apt-get update -qy
  - apt-get install -y mlocate cpio
  - apt-get install -y gfortran
  - apt-get install -y swig
  - apt-get install -y python3 python3-dev python3-numpy python3-libxml2
  - python3 --version
  - ./setup.sh /usr/local
  - cd /usr/local/metro/usr/bin
  - python3 ./metro --selftest
  - diff --ignore-matching-lines=".*<production-date>.*</production-date>" ../share/metro/data/selftest/roadcast.xml ../share/metro/data/selftest/roadcast_reference.xml
  - cd /usr/local/metro/usr/share/metro/data/test_suite
  - python3 test_suite.py
